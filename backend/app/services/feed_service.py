"""
动态分享服务层
处理动态的业务逻辑
"""

from typing import List, Optional, Dict, Any, Tuple
from datetime import datetime
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload, joinedload
from sqlalchemy import desc, asc, and_, or_, func, select

from app.models.feed import Feed, FeedLike, FeedComment, FeedStatus
from app.models.pet import Pet
from app.models.user import User
from app.schemas.feed import (
    FeedCreate, FeedUpdate, FeedResponse, FeedQueryParams,
    FeedCommentCreate, FeedCommentResponse
)
from app.services.redis_cache_service import cache_service, cache_strategy


class FeedService:
    """动态分享服务"""

    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def create_feed(self, feed_data: FeedCreate, user_id: int) -> Feed:
        """创建动态"""
        # 验证宠物是否属于当前用户
        stmt = select(Pet).where(
            Pet.id == feed_data.pet_id,
            Pet.owner_id == user_id
        )
        result = await self.db.execute(stmt)
        pet = result.scalar_one_or_none()

        if not pet:
            raise ValueError("宠物不存在或不属于当前用户")

        # 创建动态
        feed = Feed(
            pet_id=feed_data.pet_id,
            user_id=user_id,
            content=feed_data.content,
            images=feed_data.images,
            mood=feed_data.mood,
            tags=feed_data.tags,
            location=feed_data.location,
            is_public=feed_data.is_public,
            status=FeedStatus.PUBLISHED,
            published_at=datetime.utcnow()
        )

        self.db.add(feed)
        await self.db.commit()
        await self.db.refresh(feed)

        # 加载关联对象
        from sqlalchemy import select
        from sqlalchemy.orm import selectinload

        query = select(Feed).options(
            selectinload(Feed.pet),
            selectinload(Feed.user)
        ).filter(Feed.id == feed.id)

        result = await self.db.execute(query)
        feed_with_relations = result.scalar_one()

        return feed_with_relations
    
    async def get_feed_by_id(self, feed_id: int, user_id: Optional[int] = None) -> Optional[Feed]:
        """根据ID获取动态"""
        from sqlalchemy import select
        from sqlalchemy.orm import selectinload

        query = select(Feed).options(
            selectinload(Feed.pet),
            selectinload(Feed.user)
        ).filter(Feed.id == feed_id)

        # 如果不是公开动态，需要验证权限
        if user_id:
            query = query.filter(
                or_(
                    Feed.is_public == True,
                    Feed.user_id == user_id
                )
            )
        else:
            query = query.filter(Feed.is_public == True)

        result = await self.db.execute(query)
        feed = result.scalar_one_or_none()

        if feed:
            # 增加浏览数
            feed.increment_views()
            await self.db.commit()

        return feed
    
    async def get_feeds(self, params: FeedQueryParams, user_id: Optional[int] = None) -> Tuple[List[Feed], int]:
        """获取动态列表"""
        # 构建基础查询条件
        conditions = [Feed.status == FeedStatus.PUBLISHED]

        # 权限过滤
        if user_id:
            conditions.append(
                or_(
                    Feed.is_public == True,
                    Feed.user_id == user_id
                )
            )
        else:
            conditions.append(Feed.is_public == True)

        # 条件过滤
        if params.pet_id:
            conditions.append(Feed.pet_id == params.pet_id)

        if params.user_id:
            conditions.append(Feed.user_id == params.user_id)

        if params.mood:
            conditions.append(Feed.mood == params.mood)

        if params.tags:
            # 标签过滤（包含任一标签）
            tag_conditions = []
            for tag in params.tags:
                tag_conditions.append(func.json_contains(Feed.tags, f'"{tag}"'))
            conditions.append(or_(*tag_conditions))

        if params.is_featured is not None:
            conditions.append(Feed.is_featured == params.is_featured)

        # 获取总数
        count_stmt = select(func.count(Feed.id)).where(and_(*conditions))
        count_result = await self.db.execute(count_stmt)
        total = count_result.scalar()

        # 排序
        if params.sort_by == "created_at":
            order_field = Feed.created_at
        elif params.sort_by == "likes_count":
            order_field = Feed.likes_count
        elif params.sort_by == "views_count":
            order_field = Feed.views_count
        else:
            order_field = Feed.created_at

        if params.sort_order == "desc":
            order_clause = desc(order_field)
        else:
            order_clause = asc(order_field)

        # 分页查询
        offset = (params.page - 1) * params.size
        stmt = select(Feed).options(
            selectinload(Feed.pet),
            selectinload(Feed.user)
        ).where(and_(*conditions)).order_by(order_clause).offset(offset).limit(params.size)

        result = await self.db.execute(stmt)
        feeds = result.scalars().all()

        return list(feeds), total
    
    def update_feed(self, feed_id: int, feed_data: FeedUpdate, user_id: int) -> Optional[Feed]:
        """更新动态"""
        feed = self.db.query(Feed).filter(
            Feed.id == feed_id,
            Feed.user_id == user_id
        ).first()
        
        if not feed:
            return None
        
        # 更新字段
        update_data = feed_data.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(feed, field, value)
        
        self.db.commit()
        self.db.refresh(feed)
        
        return feed
    
    def delete_feed(self, feed_id: int, user_id: int) -> bool:
        """删除动态"""
        feed = self.db.query(Feed).filter(
            Feed.id == feed_id,
            Feed.user_id == user_id
        ).first()
        
        if not feed:
            return False
        
        # 软删除
        feed.status = FeedStatus.DELETED
        self.db.commit()
        
        return True
    
    def like_feed(self, feed_id: int, user_id: int) -> Tuple[bool, int]:
        """点赞/取消点赞动态"""
        # 检查动态是否存在
        feed = self.db.query(Feed).filter(Feed.id == feed_id).first()
        if not feed:
            raise ValueError("动态不存在")
        
        # 检查是否已点赞
        existing_like = self.db.query(FeedLike).filter(
            FeedLike.feed_id == feed_id,
            FeedLike.user_id == user_id
        ).first()
        
        if existing_like:
            # 取消点赞
            self.db.delete(existing_like)
            feed.decrement_likes()
            is_liked = False
        else:
            # 添加点赞
            like = FeedLike(feed_id=feed_id, user_id=user_id)
            self.db.add(like)
            feed.increment_likes()
            is_liked = True
        
        self.db.commit()
        
        return is_liked, feed.likes_count
    
    def check_user_liked(self, feed_id: int, user_id: int) -> bool:
        """检查用户是否已点赞"""
        like = self.db.query(FeedLike).filter(
            FeedLike.feed_id == feed_id,
            FeedLike.user_id == user_id
        ).first()
        
        return like is not None
    
    def get_user_feeds(self, user_id: int, page: int = 1, size: int = 20) -> Tuple[List[Feed], int]:
        """获取用户的动态列表"""
        query = self.db.query(Feed).options(
            joinedload(Feed.pet),
            joinedload(Feed.user)
        ).filter(
            Feed.user_id == user_id,
            Feed.status == FeedStatus.PUBLISHED
        ).order_by(desc(Feed.created_at))
        
        total = query.count()
        offset = (page - 1) * size
        feeds = query.offset(offset).limit(size).all()
        
        return feeds, total
    
    def get_pet_feeds(self, pet_id: int, page: int = 1, size: int = 20) -> Tuple[List[Feed], int]:
        """获取宠物的动态列表"""
        query = self.db.query(Feed).options(
            joinedload(Feed.pet),
            joinedload(Feed.user)
        ).filter(
            Feed.pet_id == pet_id,
            Feed.status == FeedStatus.PUBLISHED,
            Feed.is_public == True
        ).order_by(desc(Feed.created_at))
        
        total = query.count()
        offset = (page - 1) * size
        feeds = query.offset(offset).limit(size).all()
        
        return feeds, total

    def create_comment(self, feed_id: int, comment_data: FeedCommentCreate, user_id: int) -> FeedComment:
        """创建评论"""
        # 检查动态是否存在
        feed = self.db.query(Feed).filter(Feed.id == feed_id).first()
        if not feed:
            raise ValueError("动态不存在")

        # 如果是回复评论，检查父评论是否存在
        if comment_data.parent_id:
            parent_comment = self.db.query(FeedComment).filter(
                FeedComment.id == comment_data.parent_id,
                FeedComment.feed_id == feed_id
            ).first()
            if not parent_comment:
                raise ValueError("父评论不存在")

        # 创建评论
        comment = FeedComment(
            feed_id=feed_id,
            user_id=user_id,
            parent_id=comment_data.parent_id,
            content=comment_data.content
        )

        self.db.add(comment)

        # 增加动态的评论数
        feed.increment_comments()

        self.db.commit()
        self.db.refresh(comment)

        return comment

    def get_feed_comments(self, feed_id: int, page: int = 1, size: int = 20) -> Tuple[List[FeedComment], int]:
        """获取动态的评论列表"""
        # 只获取顶级评论（非回复）
        query = self.db.query(FeedComment).options(
            joinedload(FeedComment.user),
            joinedload(FeedComment.replies)
        ).filter(
            FeedComment.feed_id == feed_id,
            FeedComment.parent_id.is_(None),
            FeedComment.is_deleted == False
        ).order_by(desc(FeedComment.created_at))

        total = query.count()
        offset = (page - 1) * size
        comments = query.offset(offset).limit(size).all()

        return comments, total

    def delete_comment(self, comment_id: int, user_id: int) -> bool:
        """删除评论"""
        comment = self.db.query(FeedComment).filter(
            FeedComment.id == comment_id,
            FeedComment.user_id == user_id
        ).first()

        if not comment:
            return False

        # 软删除
        comment.is_deleted = True

        # 减少动态的评论数
        feed = self.db.query(Feed).filter(Feed.id == comment.feed_id).first()
        if feed:
            feed.decrement_comments()

        self.db.commit()

        return True

    def get_trending_feeds(self, page: int = 1, size: int = 20, days: int = 7) -> Tuple[List[Feed], int]:
        """获取热门动态"""
        from datetime import datetime, timedelta

        # 计算热门度分数的时间范围
        since_date = datetime.utcnow() - timedelta(days=days)

        # 热门度算法：点赞数 * 2 + 评论数 * 3 + 分享数 * 5 + 浏览数 * 0.1
        query = self.db.query(Feed).options(
            joinedload(Feed.pet),
            joinedload(Feed.user)
        ).filter(
            Feed.status == FeedStatus.PUBLISHED,
            Feed.is_public == True,
            Feed.created_at >= since_date
        )

        # 按热门度排序
        feeds = query.all()

        # 计算热门度分数并排序
        def calculate_trending_score(feed):
            # 基础分数
            score = (
                feed.likes_count * 2 +
                feed.comments_count * 3 +
                feed.shares_count * 5 +
                feed.views_count * 0.1
            )

            # 时间衰减因子（越新的动态权重越高）
            hours_ago = (datetime.utcnow() - feed.created_at).total_seconds() / 3600
            time_decay = max(0.1, 1 - (hours_ago / (24 * days)))

            return score * time_decay

        feeds.sort(key=calculate_trending_score, reverse=True)

        total = len(feeds)
        offset = (page - 1) * size
        feeds = feeds[offset:offset + size]

        return feeds, total

    def get_recommended_feeds(self, user_id: int, page: int = 1, size: int = 20) -> Tuple[List[Feed], int]:
        """获取推荐动态"""
        # 获取用户的宠物品种和兴趣
        user_pets = self.db.query(Pet).filter(Pet.owner_id == user_id).all()
        user_breeds = [pet.breed for pet in user_pets]

        # 获取用户点赞过的动态标签
        liked_feeds = self.db.query(Feed).join(FeedLike).filter(
            FeedLike.user_id == user_id
        ).limit(50).all()

        user_interests = []
        for feed in liked_feeds:
            if feed.tag_list:
                user_interests.extend(feed.tag_list)

        # 统计兴趣标签频率
        from collections import Counter
        interest_counter = Counter(user_interests)
        top_interests = [tag for tag, _ in interest_counter.most_common(10)]

        # 构建推荐查询
        query = self.db.query(Feed).options(
            joinedload(Feed.pet),
            joinedload(Feed.user)
        ).filter(
            Feed.status == FeedStatus.PUBLISHED,
            Feed.is_public == True,
            Feed.user_id != user_id  # 排除自己的动态
        )

        # 获取所有候选动态
        all_feeds = query.all()

        # 计算推荐分数
        def calculate_recommendation_score(feed):
            score = 0

            # 品种匹配加分
            if feed.pet and feed.pet.breed in user_breeds:
                score += 3

            # 标签匹配加分
            if feed.tag_list:
                for tag in feed.tag_list:
                    if tag in top_interests:
                        score += 2

            # 质量分数加分
            if feed.ai_quality_score:
                score += feed.ai_quality_score

            # 互动数据加分
            score += (feed.likes_count * 0.1 + feed.comments_count * 0.2)

            return score

        # 过滤和排序
        scored_feeds = [(feed, calculate_recommendation_score(feed)) for feed in all_feeds]
        scored_feeds = [item for item in scored_feeds if item[1] > 0]  # 只保留有分数的
        scored_feeds.sort(key=lambda x: x[1], reverse=True)

        feeds = [item[0] for item in scored_feeds]

        total = len(feeds)
        offset = (page - 1) * size
        feeds = feeds[offset:offset + size]

        return feeds, total

    def get_user_timeline(self, user_id: int, page: int = 1, size: int = 20) -> Tuple[List[Feed], int]:
        """获取用户时间线（关注的用户动态）"""
        # 简化实现：获取所有公开动态，按时间排序
        # 实际项目中应该基于用户关注关系
        query = self.db.query(Feed).options(
            joinedload(Feed.pet),
            joinedload(Feed.user)
        ).filter(
            Feed.status == FeedStatus.PUBLISHED,
            Feed.is_public == True
        ).order_by(desc(Feed.created_at))

        total = query.count()
        offset = (page - 1) * size
        feeds = query.offset(offset).limit(size).all()

        return feeds, total

    def moderate_content(self, feed_id: int) -> Dict[str, Any]:
        """内容审核"""
        feed = self.db.query(Feed).filter(Feed.id == feed_id).first()
        if not feed:
            return {"error": "动态不存在"}

        # 简单的内容审核逻辑
        content = feed.content.lower()

        # 敏感词检测
        sensitive_words = ["垃圾", "广告", "骗子", "违法", "暴力"]
        violations = []

        for word in sensitive_words:
            if word in content:
                violations.append(f"包含敏感词: {word}")

        # 内容长度检查
        if len(feed.content) < 10:
            violations.append("内容过短")
        elif len(feed.content) > 1000:
            violations.append("内容过长")

        # 图片数量检查
        if feed.image_urls and len(feed.image_urls) > 9:
            violations.append("图片数量过多")

        # 审核结果
        is_approved = len(violations) == 0

        if not is_approved:
            # 如果审核不通过，隐藏动态
            feed.status = FeedStatus.HIDDEN
            self.db.commit()

        return {
            "feed_id": feed_id,
            "is_approved": is_approved,
            "violations": violations,
            "action": "hidden" if not is_approved else "approved"
        }
